"""Tests for OAuth 1.0a functionality."""

import pytest
import requests
from unittest.mock import patch, MagicMock

from mcp_atlassian.utils.oauth import (
    OAuth1aConfig,
    configure_oauth_session,
    get_oauth_config_from_env
)


class TestOAuth1aConfig:
    """Tests for OAuth1aConfig class."""

    def test_init_with_required_params(self):
        """Test initialization with required parameters."""
        config = OAuth1aConfig(
            consumer_key="test-consumer-key",
            key_cert="test-private-key",
            access_token="test-access-token",
            access_token_secret="test-access-token-secret"
        )
        assert config.consumer_key == "test-consumer-key"
        assert config.key_cert == "test-private-key"
        assert config.access_token == "test-access-token"
        assert config.access_token_secret == "test-access-token-secret"
        assert config.signature_method == "RSA-SHA1"

    def test_init_with_custom_signature_method(self):
        """Test initialization with custom signature method."""
        config = OAuth1aConfig(
            consumer_key="test-consumer-key",
            key_cert="test-private-key",
            access_token="test-access-token",
            access_token_secret="test-access-token-secret",
            signature_method="RSA-SHA256"
        )
        assert config.signature_method == "RSA-SHA256"

    def test_from_env_success(self):
        """Test successful creation from environment variables."""
        env_vars = {
            "ATLASSIAN_OAUTH1_CONSUMER_KEY": "env-consumer-key",
            "ATLASSIAN_OAUTH1_KEY_CERT": "env-private-key",
            "ATLASSIAN_OAUTH1_ACCESS_TOKEN": "env-access-token",
            "ATLASSIAN_OAUTH1_ACCESS_TOKEN_SECRET": "env-access-token-secret",
            "ATLASSIAN_OAUTH1_SIGNATURE_METHOD": "RSA-SHA256"
        }
        with patch.dict("os.environ", env_vars):
            config = OAuth1aConfig.from_env()
            assert config is not None
            assert config.consumer_key == "env-consumer-key"
            assert config.key_cert == "env-private-key"
            assert config.access_token == "env-access-token"
            assert config.access_token_secret == "env-access-token-secret"
            assert config.signature_method == "RSA-SHA256"

    def test_from_env_missing_required_vars(self):
        """Test creation from environment with missing required variables."""
        env_vars = {
            "ATLASSIAN_OAUTH1_CONSUMER_KEY": "env-consumer-key",
            # Missing other required variables
        }
        with patch.dict("os.environ", env_vars, clear=True):
            config = OAuth1aConfig.from_env()
            assert config is None

    def test_to_oauth_dict(self):
        """Test conversion to OAuth dictionary format."""
        config = OAuth1aConfig(
            consumer_key="test-consumer-key",
            key_cert="test-private-key",
            access_token="test-access-token",
            access_token_secret="test-access-token-secret",
            signature_method="RSA-SHA256"
        )
        oauth_dict = config.to_oauth_dict()
        assert oauth_dict == {
            "consumer_key": "test-consumer-key",
            "key_cert": "test-private-key",
            "signature_method": "RSA-SHA256",
            "access_token": "test-access-token",
            "access_token_secret": "test-access-token-secret"
        }


class TestOAuth1aSessionConfiguration:
    """Tests for OAuth 1.0a session configuration."""

    def test_configure_oauth_session_success(self):
        """Test successful OAuth 1.0a session configuration."""
        session = requests.Session()
        config = OAuth1aConfig(
            consumer_key="test-consumer-key",
            key_cert="test-private-key",
            access_token="test-access-token",
            access_token_secret="test-access-token-secret"
        )
        
        result = configure_oauth_session(session, config)
        
        assert result is True
        # Check that the session was configured (auth is not None)
        assert session.auth is not None

    def test_configure_oauth_session_failure(self):
        """Test failed OAuth 1.0a session configuration."""
        session = requests.Session()
        config = OAuth1aConfig(
            consumer_key="test-consumer-key",
            key_cert="test-private-key",
            access_token="test-access-token",
            access_token_secret="test-access-token-secret"
        )
        
        # Temporarily modify the module to simulate requests_oauthlib not being available
        original_oauth1 = getattr(__import__("mcp_atlassian.utils.oauth"), "OAuth1", None)
        original_oauth1_available = getattr(__import__("mcp_atlassian.utils.oauth"), "OAUTH1_AVAILABLE", None)
        try:
            # Set OAuth1 to None and OAUTH1_AVAILABLE to False to simulate import failure
            setattr(__import__("mcp_atlassian.utils.oauth"), "OAuth1", None)
            setattr(__import__("mcp_atlassian.utils.oauth"), "OAUTH1_AVAILABLE", False)
            
            print(f"OAUTH1_AVAILABLE: {getattr(__import__('mcp_atlassian.utils.oauth'), 'OAUTH1_AVAILABLE')}")
            print(f"OAuth1: {getattr(__import__('mcp_atlassian.utils.oauth'), 'OAuth1')}")
            
            result = configure_oauth_session(session, config)
            print(f"Result: {result}")
            
            assert result is False
            assert session.auth is None
        finally:
            # Restore original values
            setattr(__import__("mcp_atlassian.utils.oauth"), "OAuth1", original_oauth1)
            setattr(__import__("mcp_atlassian.utils.oauth"), "OAUTH1_AVAILABLE", original_oauth1_available)

    def test_configure_oauth_session_import_error(self):
        """Test OAuth 1.0a session configuration when requests_oauthlib is not available."""
        session = requests.Session()
        config = OAuth1aConfig(
            consumer_key="test-consumer-key",
            key_cert="test-private-key",
            access_token="test-access-token",
            access_token_secret="test-access-token-secret"
        )
        
        # Mock the import to raise ImportError
        with patch.dict("sys.modules", {"requests_oauthlib": None}):
            result = configure_oauth_session(session, config)
            assert result is False


class TestGetOAuthConfigFromEnv:
    """Tests for get_oauth_config_from_env function with OAuth 1.0a."""

    def test_oauth1a_config_precedence(self):
        """Test that OAuth 1.0a config takes precedence when available."""
        env_vars = {
            # OAuth 1.0a variables
            "ATLASSIAN_OAUTH1_CONSUMER_KEY": "oauth1-consumer-key",
            "ATLASSIAN_OAUTH1_KEY_CERT": "oauth1-private-key",
            "ATLASSIAN_OAUTH1_ACCESS_TOKEN": "oauth1-access-token",
            "ATLASSIAN_OAUTH1_ACCESS_TOKEN_SECRET": "oauth1-access-token-secret",
            # OAuth 2.0 variables (should be ignored)
            "ATLASSIAN_OAUTH_CLIENT_ID": "oauth2-client-id",
            "ATLASSIAN_OAUTH_CLIENT_SECRET": "oauth2-client-secret",
            "ATLASSIAN_OAUTH_REDIRECT_URI": "https://example.com/callback",
            "ATLASSIAN_OAUTH_SCOPE": "read:jira-work",
        }
        
        with patch.dict("os.environ", env_vars, clear=True):
            config = get_oauth_config_from_env()
            assert config is not None
            assert isinstance(config, OAuth1aConfig)
            assert config.consumer_key == "oauth1-consumer-key"

    def test_fallback_to_oauth2_when_oauth1a_not_available(self):
        """Test fallback to OAuth 2.0 when OAuth 1.0a is not configured."""
        env_vars = {
            # Only OAuth 2.0 variables
            "ATLASSIAN_OAUTH_CLIENT_ID": "oauth2-client-id",
            "ATLASSIAN_OAUTH_CLIENT_SECRET": "oauth2-client-secret",
            "ATLASSIAN_OAUTH_REDIRECT_URI": "https://example.com/callback",
            "ATLASSIAN_OAUTH_SCOPE": "read:jira-work",
            "ATLASSIAN_OAUTH_CLOUD_ID": "test-cloud-id",
        }
        
        with patch.dict("os.environ", env_vars, clear=True):
            with patch("mcp_atlassian.utils.oauth.OAuthConfig.load_tokens", return_value={}):
                config = get_oauth_config_from_env()
                assert config is not None
                from mcp_atlassian.utils.oauth import OAuthConfig
                assert isinstance(config, OAuthConfig)
                assert config.client_id == "oauth2-client-id"
